================================================================================
                    HVNC (Hidden Virtual Network Computing) 
                           Complete Usage Guide
================================================================================

OVERVIEW:
HVNC allows you to remotely control a computer through a hidden desktop session.
The system consists of two main components:
- Server.exe: Runs on the controlling machine (your computer)
- Client.exe: Runs on the target machine (remote computer)

================================================================================
                              QUICK START
================================================================================

1. BUILD THE PROJECT:
   - Run: build.bat
   - This creates Server.exe and Client.exe in their respective build folders

2. BASIC SETUP:
   - Run Server.exe on your controlling computer
   - Run Client.exe on the target computer
   - The server will show a new window for each connected client

================================================================================
                            SERVER.EXE USAGE
================================================================================

STARTING THE SERVER:
1. Double-click Server.exe or run from command line
2. A console window will appear asking for the port number
3. Enter a port number (recommended: 4444, 8080, or any unused port)
4. Press Enter - the server will start listening

CONSOLE OUTPUT:
- "[+] Listening on Port: XXXX" - Server is ready
- "[+] New Connection: IP_ADDRESS" - Client connected
- "[!] Client IP_ADDRESS Disconnected" - Client disconnected

SERVER WINDOW CONTROLS:
When a client connects, a new window opens with the title:
"Desktop@IP_ADDRESS | HVNC - Stable [v2.0]"

RIGHT-CLICK MENU OPTIONS:
- Start Explorer: Opens Windows Explorer on target
- Start Run Dialog: Opens Windows Run dialog (Win+R)
- Start Chrome: Launches Google Chrome browser
- Start Edge: Launches Microsoft Edge browser  
- Start Brave: Launches Brave browser
- Start Firefox: Launches Mozilla Firefox
- Start Internet Explorer: Launches IE
- Start PowerShell: Opens PowerShell terminal
- Fullscreen: Toggle fullscreen mode

KEYBOARD SHORTCUTS:
- F11: Toggle fullscreen mode
- Alt+F4: Close the connection window
- Ctrl+Alt+Del: Send Ctrl+Alt+Del to target (if supported)

WINDOW FEATURES:
- Resizable: Drag corners/edges to resize
- Minimize/Maximize: Standard window controls
- Real-time screen updates from target computer
- Mouse and keyboard input forwarding

================================================================================
                            CLIENT.EXE USAGE
================================================================================

RUNNING THE CLIENT:
1. Copy Client.exe to the target computer
2. Double-click Client.exe or run from command line
3. The client will automatically connect to the server
4. No visible window appears (runs hidden)

CLIENT BEHAVIOR:
- Connects to server automatically
- Creates hidden desktop session
- Captures screen and sends to server
- Receives mouse/keyboard input from server
- Runs silently in background

STOPPING THE CLIENT:
- Close the server connection window
- Use Task Manager to end Client.exe process
- Restart the target computer

================================================================================
                           NETWORK CONFIGURATION
================================================================================

DEFAULT SETTINGS:
- The client is pre-configured to connect to a specific IP and port
- Default connection attempts are made automatically

FIREWALL CONSIDERATIONS:
- Server port must be open for incoming connections
- Windows Firewall may prompt for permission
- Corporate firewalls may block the connection

PORT RECOMMENDATIONS:
- 4444: Common choice, easy to remember
- 8080: HTTP alternative port
- 3389: RDP port (may be monitored)
- 5900: VNC port (may be monitored)
- Custom high ports (10000+): Less likely to be monitored

================================================================================
                          PERFORMANCE SETTINGS
================================================================================

CONFIGURATION FILE: hvnc_performance.ini

BASIC SETTINGS:
jpeg_quality=60        # Image quality (1-100, higher = better quality)
frame_rate=60          # Frames per second (15-120, higher = smoother)
compression_level=6    # Data compression (1-9, higher = smaller data)

QUICK PRESETS:
Run configure_performance.bat and choose:
1. Gaming - Maximum speed (jpeg_quality=40, frame_rate=45)
2. Office - Balanced performance (jpeg_quality=60, frame_rate=60)
3. Design - Best quality (jpeg_quality=85, frame_rate=30)

TROUBLESHOOTING CRASHES:
If the application crashes when client connects:
1. Run: enable_safe_mode.bat
2. This disables advanced optimizations
3. See CRASH_FIX_GUIDE.md for detailed help

================================================================================
                            COMMON SCENARIOS
================================================================================

SCENARIO 1: LOCAL NETWORK TESTING
1. Run Server.exe on Computer A
2. Enter port 4444
3. Run Client.exe on Computer B (same network)
4. Server window should appear showing Computer B's desktop

SCENARIO 2: REMOTE ACCESS SETUP
1. Configure port forwarding on router (if needed)
2. Run Server.exe and note the port
3. Deploy Client.exe to remote computer
4. Client connects automatically when run

SCENARIO 3: MULTIPLE CLIENTS
1. Start Server.exe once
2. Run Client.exe on multiple target computers
3. Each client gets its own window on the server
4. Switch between windows to control different computers

================================================================================
                              TROUBLESHOOTING
================================================================================

CONNECTION ISSUES:
- Check firewall settings on both computers
- Verify port is not already in use
- Ensure network connectivity between computers
- Try different port numbers

PERFORMANCE ISSUES:
- Lower jpeg_quality setting (try 30-40)
- Reduce frame_rate (try 30)
- Enable safe mode: enable_safe_mode.bat
- Check network bandwidth

APPLICATION CRASHES:
- Run enable_safe_mode.bat immediately
- See CRASH_FIX_GUIDE.md for detailed solutions
- Try different performance presets

NO DISPLAY/BLACK SCREEN:
- Right-click and select "Start Explorer"
- Try "Start Run Dialog" then type "explorer"
- Check if target computer is locked/sleeping

SLOW RESPONSE:
- Reduce image quality in hvnc_performance.ini
- Lower frame rate setting
- Check network latency
- Close unnecessary applications on target

================================================================================
                               SECURITY NOTES
================================================================================

IMPORTANT WARNINGS:
- This tool is for educational/authorized testing only
- Only use on computers you own or have explicit permission to access
- Unauthorized access to computers is illegal
- Use responsibly and ethically

DETECTION CONSIDERATIONS:
- Antivirus software may flag the executables
- Network monitoring may detect the traffic
- System administrators may notice unusual processes
- Use only in authorized environments

================================================================================
                              ADVANCED USAGE
================================================================================

COMMAND LINE OPTIONS:
Server.exe:
- No command line options (interactive port selection)

Client.exe:
- Runs automatically with compiled-in settings
- No user interaction required

CONFIGURATION FILES:
- hvnc_performance.ini: Performance settings
- hvnc_performance_safe.ini: Safe mode settings
- See PERFORMANCE_GUIDE.md for detailed configuration

AUTOMATION:
- Server can be automated with input redirection
- Client runs automatically when executed
- Suitable for scripted deployments

================================================================================
                                SUPPORT FILES
================================================================================

DOCUMENTATION:
- README.md: Project overview and features
- PERFORMANCE_GUIDE.md: Detailed performance tuning
- CRASH_FIX_GUIDE.md: Troubleshooting crashes
- BUILD_SUCCESS.md: Build status and features

CONFIGURATION TOOLS:
- configure_performance.bat: Interactive performance setup
- enable_safe_mode.bat: Enable crash-safe mode
- build.bat: Compile the project

EXAMPLE CONFIGURATIONS:
- hvnc_performance.ini: Current settings
- hvnc_performance_safe.ini: Safe mode settings

================================================================================
                              VERSION INFORMATION
================================================================================

Current Version: HVNC Stable v2.0
Build Date: July 2025
Compatibility: Windows 7, 8, 10, 11
Architecture: 32-bit (x86)

Features in this version:
- Crash-resistant design
- Basic performance optimization
- Windows 11 compatibility
- Configurable image quality
- Multiple browser support
- PowerShell integration

================================================================================

For additional help, see the included documentation files or check the project
repository for updates and community support.

================================================================================
