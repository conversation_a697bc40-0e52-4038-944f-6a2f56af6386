#include "Common.h"

// Image quality settings for performance optimization
enum ImageQuality {
    QUALITY_LOW = 30,      // Fastest, lowest quality
    QUALITY_MEDIUM = 60,   // Balanced
    QUALITY_HIGH = 85,     // Best quality, slower
    QUALITY_LOSSLESS = 100 // Lossless, slowest
};

// Performance settings structure
struct PerformanceSettings {
    ImageQuality imageQuality;
    DWORD frameRateLimit;      // FPS limit (0 = unlimited)
    BOOL useHardwareAccel;     // Use hardware acceleration if available
    BOOL adaptiveQuality;      // Automatically adjust quality based on performance
    DWORD compressionLevel;    // 1-9, higher = better compression but slower
};

// Global performance settings
extern PerformanceSettings g_perfSettings;

BOOL CW_Register(WNDPROC lpfnWndProc);
HWND CW_Create(DWORD uhid, DWORD width, DWORD height);
void CW_SetImageQuality(ImageQuality quality);
void CW_SetFrameRate(DWORD fps);
void CW_SetPerformanceSettings(const PerformanceSettings* settings);
void CW_GetPerformanceSettings(PerformanceSettings* settings);