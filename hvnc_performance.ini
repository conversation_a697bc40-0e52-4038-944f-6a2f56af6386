# HVNC Performance Configuration File
# Edit these settings to optimize performance for your system
# 
# QUICK SETUP GUIDE:
# - For MAXIMUM SPEED: Set jpeg_quality=30, frame_rate=30, compression_level=3
# - For BALANCED: Set jpeg_quality=60, frame_rate=60, compression_level=6  
# - For BEST QUALITY: Set jpeg_quality=85, frame_rate=30, compression_level=8
#
# Windows 11 users: Set windows11_optimizations=true for better compatibility

# =============================================================================
# IMAGE QUALITY SETTINGS
# =============================================================================

# JPEG compression quality (1-100)
# Lower = faster but worse quality, Higher = slower but better quality
# Recommended: 30 (fast), 60 (balanced), 85 (quality)
jpeg_quality=60

# Frame rate limit (FPS)
# Lower = less CPU usage, Higher = smoother but more CPU intensive
# Recommended: 30 (efficient), 60 (smooth), 120 (very smooth)
frame_rate=60

# Compression level for network data (1-9)
# Lower = faster but larger data, Higher = slower but smaller data
# Recommended: 3 (fast), 6 (balanced), 8 (efficient)
compression_level=6

# Use hardware acceleration if available (true/false)
# Recommended: true (unless you have graphics driver issues)
hardware_accel=true

# Automatically adjust quality based on performance (true/false)
# Recommended: true (helps maintain smooth performance)
adaptive_quality=true

# =============================================================================
# WINDOWS OPTIMIZATIONS
# =============================================================================

# Disable Windows Aero effects for better performance (true/false)
# Only affects Windows 7/Vista. Recommended: true for older systems
disable_aero=false

# Disable window animations (true/false)
# Can improve performance but makes Windows feel less smooth
# Recommended: false (unless you need maximum performance)
disable_animations=false

# Use direct screen capture method (true/false)
# Faster on Windows 10/11 but may not work on older systems
# Recommended: true for Windows 10/11, false for Windows 7/8
direct_capture=true

# Enable Windows 11 specific optimizations (true/false)
# IMPORTANT: Set to true if you're running Windows 11
# Recommended: true for Windows 11, false for older versions
windows11_optimizations=true

# =============================================================================
# NETWORK SETTINGS
# =============================================================================

# Socket send buffer size in bytes
# Larger = better for high-speed connections, smaller = better for slow connections
# Recommended: 32768 (32KB) for most connections
send_buffer_size=32768

# Socket receive buffer size in bytes
# Should match send_buffer_size in most cases
receive_buffer_size=32768

# Enable network data compression (true/false)
# Reduces bandwidth usage but increases CPU usage
# Recommended: true for slow connections, false for fast local networks
use_compression=true

# Minimum data size to compress (bytes)
# Data smaller than this won't be compressed
# Recommended: 1024 (1KB)
compression_threshold=1024

# =============================================================================
# PROFILE INFORMATION
# =============================================================================

# Profile name for identification
profile_name=Custom

# =============================================================================
# PRESET CONFIGURATIONS (uncomment one section to use)
# =============================================================================

# GAMING PRESET - Maximum performance for gaming
# jpeg_quality=40
# frame_rate=45
# compression_level=4
# hardware_accel=true
# adaptive_quality=true
# direct_capture=true
# windows11_optimizations=true
# use_compression=true
# profile_name=Gaming

# OFFICE PRESET - Balanced for office work
# jpeg_quality=60
# frame_rate=60
# compression_level=6
# hardware_accel=true
# adaptive_quality=true
# direct_capture=true
# windows11_optimizations=true
# use_compression=true
# profile_name=Office

# DESIGN PRESET - Best quality for design work
# jpeg_quality=85
# frame_rate=30
# compression_level=8
# hardware_accel=false
# adaptive_quality=false
# direct_capture=false
# windows11_optimizations=true
# use_compression=false
# profile_name=Design

# =============================================================================
# TROUBLESHOOTING
# =============================================================================
#
# If you experience issues:
#
# 1. SLOW PERFORMANCE:
#    - Lower jpeg_quality to 30-40
#    - Lower frame_rate to 30
#    - Set compression_level to 3-4
#    - Enable adaptive_quality
#
# 2. POOR IMAGE QUALITY:
#    - Increase jpeg_quality to 70-85
#    - Disable adaptive_quality
#    - Set compression_level to 6-8
#
# 3. WINDOWS 11 COMPATIBILITY ISSUES:
#    - Set windows11_optimizations=true
#    - Set direct_capture=true
#    - Try disabling hardware_accel if you see artifacts
#
# 4. NETWORK ISSUES:
#    - Enable use_compression for slow connections
#    - Increase buffer sizes for fast connections
#    - Lower compression_threshold for better compression
#
